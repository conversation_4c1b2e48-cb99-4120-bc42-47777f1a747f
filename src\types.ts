

// Animal and Color Types
export type AnimalName = 'eagle' | 'koala' | 'wolf' | 'dog';
export type ColorName = 'cyan' | 'green' | 'yellow';

export interface CardPosition {
  x: number;
  y: number;
}

export interface GameEndSceneData {
  score: number;
}

// TicTaps Platform Types
export interface TicTapsMessage {
  type: 'gameReady' | 'gameScore' | 'gameQuit';
  score?: number;
}

// Category Tints for Visual Effects
export interface CategoryTints {
  [key: string]: number;
}

// Utility Types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
