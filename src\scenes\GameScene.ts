import TicTapsConnector from '../utils/TicTapsConnector';
import MatchingCard from '../objects/MatchingCard';

export default class GameScene extends Phaser.Scene {
  // Animal and color definitions
  private animalNames: string[] = ['eagle', 'koala', 'wolf', 'dog'];
  private colorNames: string[] = ['cyan', 'green', 'yellow'];

  // 2D array to store animal images by [animal][color]
  private animalImages: string[][] = [];

  // Game elements
  private mainImage!: Phaser.GameObjects.Image;
  private optionCards: MatchingCard[] = [];
  private correctCardIndex: number = 0;
  private cardSize: number = 150; // Store card size as class property

  // UI elements
  private scoreText!: any; // Custom object with setText method
  private timeText!: Phaser.GameObjects.Text | Phaser.GameObjects.BitmapText;
  private timerBar!: Phaser.GameObjects.Image;
  private timerMask!: Phaser.GameObjects.Graphics;
  private bonusScoreText!: Phaser.GameObjects.Text;

  // UI panels
  private countdownPanel!: Phaser.GameObjects.Container;
  private gamePanel!: Phaser.GameObjects.Container;

  // Game state
  private score: number = 0;
  private isLocked: boolean = false;
  private isGameOver: boolean = false;
  private gameTime: number = 20; // 20 seconds game length
  private roundTime: number = 5; // 5 seconds per round
  private currentRoundTime: number = 0;
  private gameTimer!: Phaser.Time.TimerEvent;
  private roundTimer!: Phaser.Time.TimerEvent;

  constructor() {
    super('GameScene');
  }

  create(): void {
    // Get the animal images from the registry
    this.animalImages = this.game.registry.get('animalImages') || [];

    // Enable lights for WebGL renderer
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      this.lights.enable();
      this.lights.setAmbientColor(0x000000); // Set to black for maximum contrast with glowing elements
    }

    // Set a solid background color that matches the game_bg.png
    // This is a dark blue color that matches the game background image
    this.cameras.main.setBackgroundColor('#0E0F1E');

    // Create a separate background container with gradient that matches the game background
    const backgroundContainer = this.add.container(0, 0);
    backgroundContainer.setDepth(-10); // Extremely low depth to ensure it's behind everything

    // Create a gradient background that matches the game_bg.png
    // First, create a canvas texture for the gradient
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;
    const gradientTexture = this.textures.createCanvas('gradientBg', width, height);

    if (gradientTexture) {
      const context = gradientTexture.getContext();

      // Create a radial gradient from center to edges (dark blue to darker blue)
      const gradient = context.createRadialGradient(
        width/2, height/2, 0,           // inner circle center and radius
        width/2, height/2, height * 0.8  // outer circle center and radius
      );

      // Add color stops that match the game_bg.png
      gradient.addColorStop(0, '#151B30');  // Dark blue at center
      gradient.addColorStop(1, '#0E0F1E');  // Darker blue at edges

      // Fill the canvas with the gradient
      context.fillStyle = gradient;
      context.fillRect(0, 0, width, height);

      // Add some subtle noise/texture
      for (let i = 0; i < 5000; i++) {
        const x = Math.random() * width;
        const y = Math.random() * height;
        const size = Math.random() * 2;
        const alpha = Math.random() * 0.05; // Very subtle

        context.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        context.fillRect(x, y, size, size);
      }

      // Refresh the texture
      gradientTexture.refresh();

      // Create an image using this texture
      const gradientBg = this.add.image(0, 0, 'gradientBg').setOrigin(0, 0);
      backgroundContainer.add(gradientBg);

      console.log('Created gradient background as fallback');
    } else {
      // If canvas creation fails, fall back to a simple rectangle
      const baseRect = this.add.rectangle(
        0, 0,
        this.cameras.main.width,
        this.cameras.main.height,
        0x0E0F1E // Dark blue color
      ).setOrigin(0, 0);
      backgroundContainer.add(baseRect);
    }

    // Create the background image with explicit dimensions
    const bgImage = this.add.image(0, 0, 'game_background').setOrigin(0, 0);

    // Scale the image to fit the screen
    bgImage.displayWidth = this.cameras.main.width;
    bgImage.displayHeight = this.cameras.main.height;
    backgroundContainer.add(bgImage);

    // Create the main game panel
    this.gamePanel = this.add.container(0, 0);
    this.gamePanel.setVisible(false);
    this.gamePanel.setDepth(1);

    // Create the countdown panel
    this.createCountdownPanel();

    // Create UI elements in the game panel
    this.createUI();

    // Start the countdown
    this.startCountdown();
  }

  /**
   * Create the countdown panel with countdown image
   */
  private createCountdownPanel(): void {
    // Create countdown panel container
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2); // Ensure countdown panel is above both background and game panel

    // Add semi-transparent overlay
    const overlay = this.add.rectangle(
      0, 0, 
      this.cameras.main.width, 
      this.cameras.main.height, 
      0x000000, 0.7
    ).setOrigin(0, 0);

    this.countdownPanel.add(overlay);

    // Create countdown image (scaled to half size)
    const countdownImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2,
      'countdown-3'
    ).setScale(0);

    this.countdownPanel.add(countdownImage);

    // Store reference to the image for animation
    this.countdownPanel.setData('image', countdownImage);
  }

  /**
   * Start the game countdown sequence
   */
  private async startCountdown(): Promise<void> {
    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];
    const countdownImage = this.countdownPanel.getData('image');

    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImage, countdownImages[i], i === countdownImages.length - 1);
    }

    this.countdownPanel.visible = false;
    this.gamePanel.visible = true;

    console.log('Game panel visibility:', this.gamePanel.visible);
    console.log('Game started. Panel should be visible now.');

    // Start the game timer
    this.startGameTimer();

    // Set up initial round
    this.setupRound();
  }

  /**
   * Play a single step of the countdown animation
   */
  private playCountdownStep(image: Phaser.GameObjects.Image, texture: string, isGo: boolean): Promise<void> {
    return new Promise((resolve) => {
      image.setTexture(texture);
      image.setScale(0);

      try {
        this.sound.play(isGo ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      this.tweens.add({
        targets: image,
        scale: 0.2,
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: image,
              scale: 0,
              duration: 300,
              ease: 'Back.easeIn',
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }

  /**
   * Create the new top timer bar based on the updated design
   */
  private createTopTimerBar(): void {
    // Container for the timer bar elements
    const timerContainer = this.add.container(0, 0);
    this.gamePanel.add(timerContainer);

    // Timer bar dimensions and position
    const timerBarWidth = this.cameras.main.width * 0.76; // Match the width in image 2
    const timerBarHeight = 40; // Height to match the image
    const timerBarY = 50; // Position from the top
    const timerBarX = this.cameras.main.width / 2;

    // Create a rounded rectangle background for the timer
    const bgRect = this.add.graphics();
    bgRect.fillStyle(0x222222, 1); // Dark background color
    bgRect.fillRoundedRect(
      timerBarX - timerBarWidth/2,
      timerBarY - timerBarHeight/2,
      timerBarWidth,
      timerBarHeight,
      timerBarHeight/2 // Corner radius (half of height for pill shape)
    );
    timerContainer.add(bgRect);

    // Add a border to the background
    const borderGraphics = this.add.graphics();
    borderGraphics.lineStyle(2, 0x333333, 1); // Gray border
    borderGraphics.strokeRoundedRect(
      timerBarX - timerBarWidth/2,
      timerBarY - timerBarHeight/2,
      timerBarWidth,
      timerBarHeight,
      timerBarHeight/2 // Same corner radius
    );
    timerContainer.add(borderGraphics);

    // Try to add the SVG background image if it exists
    if (this.textures.exists('timer_bg')) {
      const bgBar = this.add.image(
        timerBarX,
        timerBarY,
        'timer_bg'
      ).setOrigin(0.5);

      // Scale the background to fit the desired dimensions
      bgBar.displayWidth = timerBarWidth;
      bgBar.displayHeight = timerBarHeight;
      timerContainer.add(bgBar);

      console.log('Timer bg image added successfully');
    } else {
      console.log('Timer bg texture does not exist, using fallback graphics');
    }

    // Create the timer bar using the gradient texture first (will be underneath)
    const barWidth = timerBarWidth - 4; // Slightly smaller than background
    const barHeight = timerBarHeight - 4; // Slightly smaller than background

    // Create a canvas for the gradient
    const barTexture = this.textures.createCanvas('timerBarGradient', barWidth, barHeight);
    if (!barTexture) {
      console.error('Failed to create timer bar gradient canvas');
      return;
    }

    const barContext = barTexture.getContext();

    // Create a linear gradient from cyan to purple as in the images
    const gradient = barContext.createLinearGradient(0, 0, barWidth, 0);
    gradient.addColorStop(0, '#33DDFF'); // Light blue/cyan
    gradient.addColorStop(1, '#664DFF'); // Purple

    // Add a subtle glow effect to make it pop more
    barContext.shadowColor = '#33DDFF';
    barContext.shadowBlur = 6;
    barContext.shadowOffsetX = 0;
    barContext.shadowOffsetY = 0;

    // Fill the canvas with the gradient
    barContext.fillStyle = gradient;
    barContext.fillRect(0, 0, barWidth, barHeight);
    barTexture.refresh();

    // Create the timer bar using the gradient texture
    this.timerBar = this.add.image(timerBarX, timerBarY, 'timerBarGradient').setOrigin(0.5);
    timerContainer.add(this.timerBar);

    // Create mask for the timer bar that will be updated during countdown
    this.timerMask = this.make.graphics({});

    // Calculate how much of the bar remains visible initially (100%)
    const visibleWidth = barWidth; // Full width initially

    // Use rounded corners for the mask (similar to the background)
    const cornerRadius = 10; // Smaller than the background radius for inset look

    // Draw the timer bar with rounded corners
    this.timerMask.fillStyle(0xffffff, 1);
    this.timerMask.fillRoundedRect(
      timerBarX - barWidth/2, 
      timerBarY - barHeight/2, 
      visibleWidth, 
      barHeight,
      cornerRadius
    );

    // Apply mask to the timer bar
    const mask = new Phaser.Display.Masks.GeometryMask(this, this.timerMask);
    this.timerBar.setMask(mask);

    // Left circle with clock icon - add AFTER timer bar
    const leftCircleRadius = 24; // Size to match image
    const leftCircleX = timerBarX - timerBarWidth / 2;
    const leftCircleY = timerBarY;

    // Add a circular mask for the left circle
    const leftCircle = this.add.circle(
      leftCircleX,
      leftCircleY,
      leftCircleRadius,
      0x222222 // Dark background
    );
    leftCircle.setStrokeStyle(2, 0x33DDFF, 1); // Blue border
    timerContainer.add(leftCircle);

    // Add clock icon with highest depth to ensure it's on top
    const clockIcon = this.add.image(
      leftCircleX,
      leftCircleY,
      'timer_icon'
    ).setOrigin(0.5).setScale(0.50);
    timerContainer.add(clockIcon);

    // Right circle with time text - using the timer_countdown_bg.png image
    const rightCircleX = timerBarX + timerBarWidth / 2;
    const rightCircleY = timerBarY;

    const countdownBg = this.add.image(
      rightCircleX,
      rightCircleY,
      'timer_countdown_bg'
    ).setOrigin(0.5);

    // Scale the countdown background to match the design
    const countdownBgScale = 0.7; // Adjust this value to fit the design
    countdownBg.setScale(countdownBgScale);
    timerContainer.add(countdownBg);

    // Time remaining text (inside the countdown background)
    this.timeText = this.add.text(
      rightCircleX,
      rightCircleY,
      '20s',
      {
        fontFamily: 'Arial',
        fontSize: '24px',
        fontStyle: 'bold',
        color: '#FFFFFF' // White text
      }
    ).setOrigin(0.5);
    timerContainer.add(this.timeText);
  }

  private createUI(): void {
    // Create the new timer bar at the top
    this.createTopTimerBar();

    // Add Total Points header text with gradient
    // Create a canvas texture for the header gradient text
    const headerWidth = 200;
    const headerHeight = 50;
    const headerCanvas = this.textures.createCanvas('headerGradient', headerWidth, headerHeight);
    if (headerCanvas) {
      const headerContext = headerCanvas.getContext();

      // Clear the canvas
      headerContext.clearRect(0, 0, headerWidth, headerHeight);

      // Create gradient (horizontal gradient from left to right)
      const headerGradient = headerContext.createLinearGradient(0, 0, headerWidth, 0);
      headerGradient.addColorStop(0, '#33DDFF'); // Light blue/cyan at 0%
      headerGradient.addColorStop(1, '#664DFF'); // Purple at 100%

      // Set up text styling
      headerContext.font = 'bold 24px Arial';
      headerContext.textAlign = 'center';
      headerContext.textBaseline = 'middle';

      // Apply gradient and draw text
      headerContext.fillStyle = headerGradient;
      headerContext.fillText('Total Points', headerWidth/2, headerHeight/2);

      // Update the canvas texture
      headerCanvas.refresh();

      // Display the header texture as an image
      const headerImage = this.add.image(this.cameras.main.width / 2, 120, 'headerGradient');
      this.gamePanel.add(headerImage);
    }

    // Add score display with cyan-to-purple gradient
    const scoreX = this.cameras.main.width / 2;
    const scoreY = 175;

    // Create a canvas texture for our gradient score
    const width = 200;
    const height = 80;
    const textCanvas = this.textures.createCanvas('scoreGradient', width, height);

    if (textCanvas) {
      const context = textCanvas.getContext();

      // Clear the canvas
      context.clearRect(0, 0, width, height);

      // Create gradient (horizontal gradient from left to right)
      const gradient = context.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, '#33DDFF'); // Light blue/cyan at 0%
      gradient.addColorStop(1, '#664DFF'); // Purple at 100%

      // Set up text styling
      const scoreText = "0";
      context.font = 'bold 64px Arial';
      context.textAlign = 'center';
      context.textBaseline = 'middle';

      // Apply gradient and draw text
      context.fillStyle = gradient;
      context.fillText(scoreText, width/2, height/2);

      // Update the canvas texture
      textCanvas.refresh();

      // Display the texture as an image
      const scoreImage = this.add.image(scoreX, scoreY, 'scoreGradient');
      this.gamePanel.add(scoreImage);

      // Store this as our scoreText (we'll update it with updateScore method)
      this.scoreText = {
        setText: (value: string) => {
          if (textCanvas) {
            // Re-render the score canvas with the new value
            context.clearRect(0, 0, width, height);
            context.fillStyle = gradient;
            context.fillText(value, width/2, height/2);
            textCanvas.refresh();
          }
        }
      };
    }

    // Option cards - in a plus-shaped pattern with arrows pointing to the center
    const cardOffset = 110; // Reduced distance from center
    const centerX = this.cameras.main.width / 2;
    const centerY = this.cameras.main.height * 0.55;

    // Card positions in plus-shaped pattern with arrows pointing to center
    const cardPositions = [
      { x: centerX - cardOffset, y: centerY - cardOffset }, // Top left
      { x: centerX + cardOffset, y: centerY - cardOffset }, // Top right
      { x: centerX, y: centerY }, // Center
      { x: centerX - cardOffset, y: centerY + cardOffset }, // Bottom left
      { x: centerX + cardOffset, y: centerY + cardOffset }  // Bottom right
    ];

    // Create all cards first
    for (let i = 0; i < 5; i++) {
      // Create the card
      const card = new MatchingCard(
        this,
        cardPositions[i].x,
        cardPositions[i].y,
        'default_card',
        this.cardSize
      );

      // Store card in array but don't add to gamePanel yet
      this.optionCards.push(card);

      // Only add click handler to non-center cards
      if (i !== 2) {
        card.on('pointerdown', () => {
          this.checkAnswer(i);
        });
      }
    }

    // Now add cards to the gamePanel in the correct order (bottom to top)
    // First add all corner cards
    for (let i = 0; i < 5; i++) {
      if (i !== 2) { // Skip center card for now
        this.gamePanel.add(this.optionCards[i]);
        this.optionCards[i].setDepth(1); // Set lower depth for outer cards
      }
    }

    // Now add center card with special styling
    const centerCard = this.optionCards[2];

    // Add a solid backdrop behind the center card to block visibility of cards underneath
    // Use Graphics for rounded rectangle since Rectangle doesn't support rounded corners
    const solidBackdrop = this.add.graphics();
    solidBackdrop.fillStyle(0x181818, 1); // Dark color matching the background

    // Calculate exact position to match card perfectly
    const backdropX = cardPositions[2].x - this.cardSize/2;
    const backdropY = cardPositions[2].y - this.cardSize/2;

    // Draw rounded rectangle with 16px radius
    solidBackdrop.fillRoundedRect(
      backdropX, 
      backdropY,
      this.cardSize,
      this.cardSize,
      16 // Corner radius
    );

    // Make backdrop slightly larger to ensure full coverage
    const backdropSize = this.cardSize * 0.9;
    solidBackdrop.clear();
    solidBackdrop.fillStyle(0x181818, 1);
    solidBackdrop.fillRoundedRect(
      cardPositions[2].x - backdropSize/2, 
      cardPositions[2].y - backdropSize/2,
      backdropSize,
      backdropSize,
      16 // Corner radius
    );
    this.gamePanel.add(solidBackdrop);
    solidBackdrop.setDepth(8); // Behind the glow but above other cards

    // Add glowing border effect to the center card
    const borderGlow = this.add.image(cardPositions[2].x, cardPositions[2].y, 'card_bg')
      .setOrigin(0.5)
      .setDisplaySize(this.cardSize + 5, this.cardSize + 5) // Reduced size difference (was +10)
      .setTint(0x33DDFF) // Cyan color matching the timer
      .setAlpha(0.4); // Reduced from 0.8 to 0.4 (50% decrease)

    // Add glow effect with post-processing if WebGL is available
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      borderGlow.setBlendMode(Phaser.BlendModes.ADD);
    }

    // Add border glow first, then center card
    this.gamePanel.add(borderGlow);
    borderGlow.setDepth(9); // Just below the card but above other cards

    // Finally add center card on top of everything
    this.gamePanel.add(centerCard);
    centerCard.setDepth(10); // Higher depth value so it renders on top

    // Center card fully opaque
    centerCard.getCardBackground().setAlpha(1);
    
    // Disable interactivity on center card since it's the reference
    centerCard.disableInteractive();
  }

  private startGameTimer(): void {
    this.gameTimer = this.time.addEvent({
      delay: 1000,
      callback: this.updateGameTimer,
      callbackScope: this,
      repeat: this.gameTime
    });
  }

  private updateGameTimer(): void {
    if (this.isGameOver) return;

    // Decrease game time
    this.gameTime--;

    // Update time display with the 's' suffix for seconds
    this.timeText.setText(this.gameTime + 's');

    // Check if game is over
    if (this.gameTime <= 0) {
      this.endGame();
    }
  }

  private setupRound(): void {
    // Play round start sound
    this.sound.play('round');

    // Clear all existing lights to prevent accumulation between rounds
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      // Shutdown and re-enable lights to clear all existing lights
      this.lights.shutdown();
      this.lights.enable();
      // Reset ambient light
      this.lights.setAmbientColor(0x000000);
    }

    // Reset the timer frame to initial percentage
    // Ensure these values match those in createTopTimerBar
    const timerBarWidth = this.cameras.main.width * 0.76;
    const timerBarHeight = 40;
    const timerBarY = 50;
    const timerBarX = this.cameras.main.width / 2;

    // Bar dimensions - slightly smaller than background
    const barWidth = timerBarWidth - 4;
    const barHeight = timerBarHeight - 4;

    // Update the timer mask to show full timer bar
    this.timerMask.clear();

    // Use the same corner radius as in other places
    const cornerRadius = 10;

    // Draw the timer bar with rounded corners
    this.timerMask.fillStyle(0xffffff, 1);
    this.timerMask.fillRoundedRect(
      timerBarX - barWidth/2, 
      timerBarY - barHeight/2, 
      barWidth, 
      barHeight,
      cornerRadius
    );

    // Reset lock state
    this.isLocked = false;

    // Start round timer
    this.currentRoundTime = this.roundTime;
    this.startRoundTimer();

    // Choose a random animal and color for the correct card
    // Step 1: First, choose which animal we'll use for the correct answer
    const correctAnimalIndex = Phaser.Math.Between(0, this.animalNames.length - 1);
    const correctAnimal = this.animalNames[correctAnimalIndex];

    // Step 2: Choose which color variation of this animal to use
    const correctColorCategory = Phaser.Math.Between(0, this.colorNames.length - 1);
    const correctColor = this.colorNames[correctColorCategory];

    // The main image is the hidden one that we're matching against
    // It will be used by the logic behind the scenes, but not displayed
    const mainImageKey = this.getAnimalImageKey(correctAnimalIndex, correctColorCategory);
    this.mainImage?.setTexture(mainImageKey);

    // Set appropriate tint based on color category
    const categoryTints: Record<number, number> = {
      0: 0x66ffff, // Cyan
      1: 0x66ff66, // Green
      2: 0xffff66  // Yellow
    };

    // Log the selected animal and color for debugging
    console.log(`Main image: ${correctAnimal} in ${correctColor} (indices: ${correctAnimalIndex}, ${correctColorCategory})`);

    // The center card (index 2) should always be the guide card (matching distractor)
    // It should never be the correct card to click
    const matchingImageCardIndex = 2; // Center position is always the matching distractor

    // Choose a random position for the correct card (not center)
    do {
      this.correctCardIndex = Phaser.Math.Between(0, 4);
    } while (this.correctCardIndex === 2); // Avoid center position

    // Set up the 5 option cards
    // Create a list of all possible animal indices excluding the correct animal
    const availableAnimalIndices = Array.from(
      { length: this.animalNames.length }, 
      (_, index) => index
    ).filter(index => index !== correctAnimalIndex);

    // Shuffle the available animal indices to pick random ones
    Phaser.Utils.Array.Shuffle(availableAnimalIndices);

    // Keep track of which animals we've assigned to cards
    const usedAnimalIndices = new Set<number>();
    usedAnimalIndices.add(correctAnimalIndex); // Mark the correct animal as used

    for (let i = 0; i < this.optionCards.length; i++) {
      // Reset card selection state
      this.optionCards[i].resetSelection('');

      if (i === this.correctCardIndex) {
        // This is the correct card - use the same animal as main image but with a DIFFERENT color
        // Choose a different color than the one used for the center
        let cardColorCategory;
        do {
          cardColorCategory = Phaser.Math.Between(0, this.colorNames.length - 1);
        } while (cardColorCategory === correctColorCategory);

        const cardColor = this.colorNames[cardColorCategory];
        const cardImageKey = this.getAnimalImageKey(correctAnimalIndex, cardColorCategory);

        console.log(`Correct card (${i}): ${correctAnimal} in ${cardColor}`);

        this.optionCards[i].setCardImage(cardImageKey);
        this.optionCards[i].setCorrect(true);

        // Set the tint based on this card's color category
        this.optionCards[i].setTint(categoryTints[cardColorCategory]);

        // Add glow effect to the image if WebGL available
        if (this.sys.game.renderer.type === Phaser.WEBGL) {
          // Access the card image through the container
          const cardImage = this.optionCards[i].getAt(1) as Phaser.GameObjects.Image;
          if (cardImage) {
            cardImage.setPipeline('Light2D');
            // Increase intensity for more prominent glow
            const lightColor = categoryTints[cardColorCategory];
            // Use the card's position directly instead of the positions array
            this.lights.addLight(this.optionCards[i].x, this.optionCards[i].y, this.cardSize * 0.9, lightColor, 1.8);
          }
        }
      } else if (i === matchingImageCardIndex) {
        // This distractor card should match the main image exactly (same animal, same color)
        const matchingImageKey = this.getAnimalImageKey(correctAnimalIndex, correctColorCategory);

        console.log(`Matching distractor card (${i}): ${correctAnimal} in ${correctColor} (exact match to main)`);

        this.optionCards[i].setCardImage(matchingImageKey);
        this.optionCards[i].setCorrect(false);

        // Set the tint based on the main image's color category
        this.optionCards[i].setTint(categoryTints[correctColorCategory]);

        // Add glow effect to the image if WebGL available
        if (this.sys.game.renderer.type === Phaser.WEBGL) {
          // Access the card image through the container
          const cardImage = this.optionCards[i].getAt(1) as Phaser.GameObjects.Image;
          if (cardImage) {
            cardImage.setPipeline('Light2D');
            // Increase intensity for more prominent glow
            const lightColor = categoryTints[correctColorCategory];
            // Use the card's position directly
            this.lights.addLight(this.optionCards[i].x, this.optionCards[i].y, this.cardSize * 0.9, lightColor, 1.8);
          }
        }
      } else {
        // This is a regular distractor card - use a different animal
        // Get an animal index we haven't used yet
        // If we've used all available indices, reuse one (this can happen with the last card)
        const distractorAnimalIndex = availableAnimalIndices.length > 0 
          ? availableAnimalIndices.pop()! 
          : Array.from(usedAnimalIndices).find(index => index !== correctAnimalIndex) || 0;

        // Only add to used indices if it's not already there
        if (!usedAnimalIndices.has(distractorAnimalIndex)) {
          usedAnimalIndices.add(distractorAnimalIndex);
        }

        const distractorAnimal = this.animalNames[distractorAnimalIndex];

        // Choose a random color for this distractor
        const distractorColorCategory = Phaser.Math.Between(0, this.colorNames.length - 1);
        const distractorColor = this.colorNames[distractorColorCategory];

        const distractorImageKey = this.getAnimalImageKey(distractorAnimalIndex, distractorColorCategory);

        console.log(`Regular distractor card (${i}): ${distractorAnimal} in ${distractorColor}`);

        this.optionCards[i].setCardImage(distractorImageKey);
        this.optionCards[i].setCorrect(false);

        // Set the tint based on this card's color category and add glow effect
        this.optionCards[i].setTint(categoryTints[distractorColorCategory]);
        // Add additional glow effect to the image if WebGL available
        if (this.sys.game.renderer.type === Phaser.WEBGL) {
          // Access the card image through the container
          const cardImage = this.optionCards[i].getAt(1) as Phaser.GameObjects.Image;
          if (cardImage) {
            cardImage.setPipeline('Light2D');
            // Increase intensity for more prominent glow
            const lightColor = categoryTints[distractorColorCategory];
            // Use the card's position directly
            this.lights.addLight(this.optionCards[i].x, this.optionCards[i].y, this.cardSize * 0.9, lightColor, 1.8);
          }
        }
      }

      // Animate card image appearing with delay
      this.optionCards[i].animateCardImage(200 + i * 50);
    }
  }

  private startRoundTimer(): void {
    // Clear any existing timer
    if (this.roundTimer) {
      this.roundTimer.remove();
    }

    // Create a new timer that updates 100 times per second for smooth animation
    this.roundTimer = this.time.addEvent({
      delay: 10,
      callback: this.updateRoundTimer,
      callbackScope: this,
      repeat: this.roundTime * 100 - 1
    });
  }

  private updateRoundTimer(): void {
    if (this.isLocked || this.isGameOver) return;

    // Decrease timer by 0.01 seconds
    this.currentRoundTime -= 0.01;

    // Calculate percentage of time remaining
    const percentage = this.currentRoundTime / this.roundTime;

    // Update the timer bar mask
    // These values must match those in createTopTimerBar
    const timerBarWidth = this.cameras.main.width * 0.76;
    const timerBarHeight = 40;
    const timerBarY = 50;
    const timerBarX = this.cameras.main.width / 2;

    // Bar dimensions - slightly smaller than background to match image
    const barWidth = timerBarWidth - 4;
    const barHeight = timerBarHeight - 4;

    // Clear mask and redraw with new width
    this.timerMask.clear();

    // Calculate how much of the bar remains visible
    const visibleWidth = barWidth * percentage;

    // Use the same corner radius as defined in createTopTimerBar
    const cornerRadius = 10;

    // Draw the timer bar with rounded corners    
    if (visibleWidth > 0) {
      this.timerMask.fillStyle(0xffffff, 1);
      this.timerMask.fillRoundedRect(
        timerBarX - barWidth/2, 
        timerBarY - barHeight/2, 
        visibleWidth, 
        barHeight,
        cornerRadius
      );
    }

    // Check if round time is up
    if (this.currentRoundTime <= 0) {
      // Clear the timer to avoid multiple calls
      if (this.roundTimer) {
        this.roundTimer.remove();
      }

      // Automatically move to next round if time runs out
      this.time.delayedCall(500, this.setupRound, [], this);
    }
  }

  private checkAnswer(index: number): void {
    // Ignore if game is locked or over
    if (this.isLocked || this.isGameOver) return;

    // Lock the game temporarily
    this.isLocked = true;

    if (index === this.correctCardIndex) {
      // Correct answer
      this.sound.play('correct');

      // Mark the card as correct
      this.optionCards[index].markSelected(true);

      // Calculate score bonus based on time remaining
      const percentage = Math.floor((this.currentRoundTime / this.roundTime) * 100);

      // Update score
      this.score += percentage;

      // Check if scoreText exists before updating it
      if (this.scoreText) {
        this.scoreText.setText(this.score.toString());
      }

      // Show bonus animation with message
      this.showBonusText(`Good Choice +${percentage}`, true);

      // Clear the round timer to prevent it from continuing to tick down
      if (this.roundTimer) {
        this.roundTimer.remove();
      }

      // Set up next round after delay
      this.time.delayedCall(780, this.setupRound, [], this);
    } else {
      // Wrong answer
      this.sound.play('wrong');

      // Mark the card as wrong
      this.optionCards[index].markSelected(false);

      // Penalty for wrong answer
      const penalty = 20;
      this.score = Math.max(0, this.score - penalty);

      // Check if scoreText exists before updating it
      if (this.scoreText) {
        this.scoreText.setText(this.score.toString());
      }

      // Show penalty animation with message
      this.showBonusText(`Bad Choice -${penalty}`, false);

      // Enable selecting another card after a short delay
      this.time.delayedCall(500, () => {
        this.isLocked = false;
      });
    }
  }

  private showBonusText(text: string, isCorrect: boolean): void {
    // Create or reuse text object
    if (!this.bonusScoreText || !(this.bonusScoreText instanceof Phaser.GameObjects.Text)) {
      // Create text with the appropriate style
      this.bonusScoreText = this.add.text(
        this.cameras.main.width / 2,
        this.cameras.main.height / 2.5,
        text,
        {
          fontFamily: 'Arial',
          fontSize: '32px',
          fontStyle: 'italic',
          color: isCorrect ? '#4FFFAA' : '#FF4F59',
          stroke: '#000000',
          strokeThickness: 3,
          shadow: { offsetX: 1, offsetY: 1, color: '#000000', blur: 2, stroke: true, fill: true }
        }
      ).setOrigin(0.5).setDepth(100).setAlpha(0);

      this.gamePanel.add(this.bonusScoreText);
    } else {
      // Update existing text
      this.bonusScoreText.setText(text);
      this.bonusScoreText.setColor(isCorrect ? '#4FFFAA' : '#FF4F59');
      this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);
    }

    // Animation sequence
    const targetY = this.cameras.main.height / 2.5 - 50; // Move upward

    // Clear any existing tweens
    this.tweens.killTweensOf(this.bonusScoreText);

    // Reset position and alpha
    this.bonusScoreText.setAlpha(0);
    this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);

    // Create fade in tween
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 1,
      duration: 200,
      ease: 'Linear'
    });

    // Create move up tween
    this.tweens.add({
      targets: this.bonusScoreText,
      y: targetY,
      duration: 700,
      ease: 'Cubic.easeOut'
    });

    // Create fade out tween with delay
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 0,
      delay: 600,
      duration: 300,
      ease: 'Linear'
    });
  }

  /**
   * Get the image key for a specific animal and color
   * @param animalIndex The index of the animal
   * @param colorIndex The index of the color
   * @returns The image key
   */
  private getAnimalImageKey(animalIndex: number, colorIndex: number): string {
    // Check if the 2D array is properly initialized
    if (this.animalImages && 
        animalIndex >= 0 && animalIndex < this.animalImages.length &&
        colorIndex >= 0 && colorIndex < this.animalImages[animalIndex].length) {
      return this.animalImages[animalIndex][colorIndex];
    }

    // Fallback to the old format if the 2D array is not available
    console.warn(`Using fallback image key for animal ${animalIndex}, color ${colorIndex}`);
    return `image_${colorIndex}_${animalIndex}`;
  }

  private endGame(): void {
    if (this.isGameOver) return;

    // Set game over flag
    this.isGameOver = true;

    // Clear all timers to prevent further updates
    if (this.gameTimer) {
      this.gameTimer.remove();
    }

    if (this.roundTimer) {
      this.roundTimer.remove();
    }

    // Play end game sound
    this.sound.play('end');

    // Transition to end scene after a short delay
    this.time.delayedCall(500, () => {
      this.scene.start('GameEndScene', { score: this.score });
    });
  }
}
