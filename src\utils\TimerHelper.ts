/**
 * Helper methods for creating and updating the timer frame
 */
export class TimerHelper {
  /**
   * Update the timer frame based on percentage remaining
   * @param scene The Phaser scene
   * @param mask The graphics mask to update
   * @param percentage Value between 0.0 and 1.0
   */
  public static updateTimerFrame(scene: Phaser.Scene, mask: Phaser.GameObjects.Graphics, percentage: number): void {
    // Get frame dimensions
    const frameWidth = scene.cameras.main.width * 0.8;
    const frameHeight = scene.cameras.main.height * 0.8;
    const frameX = scene.cameras.main.width / 2;
    const frameY = scene.cameras.main.height / 2;
    const cornerRadius = 40;
    const lineWidth = 4;
    
    // Clear previous mask
    mask.clear();
    
    // To make a nice gradient frame with rounded corners, we'll create a path that follows
    // the frame and only show as much of it as the percentage dictates.
    // The path goes counterclockwise starting from top-right
    
    mask.fillStyle(0xffffff);
    
    // Calculate how much of the frame to draw based on percentage
    // Calculate total perimeter
    const perimeter = 2 * (frameWidth + frameHeight);
    
    // Calculate visible length (counterclockwise from top right)
    const visibleLength = perimeter * percentage;
    
    // Constants for each edge
    const rightEdgeLength = frameHeight;
    const bottomEdgeLength = frameWidth;
    const leftEdgeLength = frameHeight;
    const topEdgeLength = frameWidth;
    
    // Determine which edges should be drawn
    let remainingLength = visibleLength;
    
    // Create the frame with separate segments for better control
    
    // 1. RIGHT edge - always start from this edge
    const rightEdgeVisible = Math.min(rightEdgeLength, remainingLength);
    const rightPercentage = rightEdgeVisible / rightEdgeLength;
    
    if (rightPercentage > 0) {
      // Draw part or all of the right edge
      mask.fillRect(
        frameX + frameWidth/2 - lineWidth/2,
        frameY - frameHeight/2, 
        lineWidth,
        frameHeight * rightPercentage
      );
      
      remainingLength -= rightEdgeVisible;
    }
    
    // 2. BOTTOM edge
    if (remainingLength > 0) {
      const bottomEdgeVisible = Math.min(bottomEdgeLength, remainingLength);
      const bottomPercentage = bottomEdgeVisible / bottomEdgeLength;
      
      // Draw part or all of the bottom edge
      mask.fillRect(
        frameX + frameWidth/2 - bottomEdgeVisible, 
        frameY + frameHeight/2 - lineWidth/2,
        bottomEdgeVisible,
        lineWidth
      );
      
      remainingLength -= bottomEdgeVisible;
    }
    
    // 3. LEFT edge
    if (remainingLength > 0) {
      const leftEdgeVisible = Math.min(leftEdgeLength, remainingLength);
      const leftPercentage = leftEdgeVisible / leftEdgeLength;
      
      // Draw part or all of the left edge
      mask.fillRect(
        frameX - frameWidth/2 - lineWidth/2,
        frameY + frameHeight/2 - leftEdgeVisible,
        lineWidth,
        leftEdgeVisible
      );
      
      remainingLength -= leftEdgeVisible;
    }
    
    // 4. TOP edge
    if (remainingLength > 0) {
      const topEdgeVisible = Math.min(topEdgeLength, remainingLength);
      
      // Draw part or all of the top edge
      mask.fillRect(
        frameX - frameWidth/2,
        frameY - frameHeight/2 - lineWidth/2,
        topEdgeVisible,
        lineWidth
      );
    }
    
    // Now draw rounded corners on top of line segments
    // Only draw corners that should be visible based on the percentage
    
    // Top-right corner
    mask.fillCircle(
      frameX + frameWidth/2, 
      frameY - frameHeight/2, 
      lineWidth/2
    );
    
    // Bottom-right corner (if needed)
    if (visibleLength > rightEdgeLength * 0.9) {
      mask.fillCircle(
        frameX + frameWidth/2, 
        frameY + frameHeight/2, 
        lineWidth/2
      );
    }
    
    // Bottom-left corner (if needed)
    if (visibleLength > (rightEdgeLength + bottomEdgeLength) * 0.9) {
      mask.fillCircle(
        frameX - frameWidth/2, 
        frameY + frameHeight/2, 
        lineWidth/2
      );
    }
    
    // Top-left corner (if needed)
    if (visibleLength > (rightEdgeLength + bottomEdgeLength + leftEdgeLength) * 0.9) {
      mask.fillCircle(
        frameX - frameWidth/2, 
        frameY - frameHeight/2, 
        lineWidth/2
      );
    }
  }
}
