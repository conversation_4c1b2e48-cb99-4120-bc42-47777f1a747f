/**
 * TicTapsConnector - Handles communication with TicTaps platform
 */
export default class TicTapsConnector {
  private isWebGL: boolean;

  constructor() {
    this.isWebGL = this.checkIfWebGL();
  }

  /**
   * Check if running in a browser environment and embedded
   */
  private checkIfWebGL(): boolean {
    return (typeof window !== 'undefined' && window.parent && window.parent !== window);
  }

  /**
   * Notify parent window that the game is ready
   */
  public notifyGameReady(): void {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage({ type: 'gameReady' }, '*');
      console.log('Game ready notification sent to parent');
    }
  }

  /**
   * Send score to parent window
   */
  public sendScore(score: number): void {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage({ type: 'gameScore', score: score }, '*');
      console.log('Score sent to parent:', score);
    }
  }

  /**
   * Notify parent window that the game has quit
   */
  public notifyGameQuit(): void {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage({ type: 'gameQuit' }, '*');
      console.log('Game quit notification sent to parent');
    }
  }
}