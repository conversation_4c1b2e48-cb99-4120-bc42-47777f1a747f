import { TicTapsMessage } from '../types';

/**
 * TicTapsConnector - Handles communication with TicTaps platform
 */
export default class TicTapsConnector {
  private isEmbedded: boolean;

  constructor() {
    this.isEmbedded = this.checkIfEmbedded();
  }

  /**
   * Check if running in a browser environment and embedded
   */
  private checkIfEmbedded(): boolean {
    return (typeof window !== 'undefined' && window.parent && window.parent !== window);
  }

  /**
   * Send a message to the parent window
   */
  private sendMessage(message: TicTapsMessage): void {
    if (this.isEmbedded && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage(message, '*');
    }
  }

  /**
   * Notify parent window that the game is ready
   */
  public notifyGameReady(): void {
    this.sendMessage({ type: 'gameReady' });
    console.log('Game ready notification sent to parent');
  }

  /**
   * Send score to parent window
   */
  public sendScore(score: number): void {
    this.sendMessage({ type: 'gameScore', score });
    console.log('Score sent to parent:', score);
  }

  /**
   * Notify parent window that the game has quit
   */
  public notifyGameQuit(): void {
    this.sendMessage({ type: 'gameQuit' });
    console.log('Game quit notification sent to parent');
  }
}